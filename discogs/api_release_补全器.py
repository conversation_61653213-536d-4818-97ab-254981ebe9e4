#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Discogs API Release 数据补全器

功能：
1. 从release_new表检测缺失的release ID
2. 通过Discogs API获取缺失的数据
3. 转换API响应为数据库兼容格式
4. 批量插入到release_copy集合（保持原始ID）

特性：
- 进度跟踪和断点续传
- API频率限制（1秒1次）
- 错误处理和重试机制
- 批量数据库操作优化
- 详细的日志记录

作者：AI Assistant
创建时间：2025-07-28
"""

import os
import sys
import time
import json
import csv
import requests
import logging
import random
from datetime import datetime, timezone
from pymongo import MongoClient
from typing import Set, List, Dict, Optional
import signal

# 导入现有的枚举类
try:
    from release.enums import Permissions, Status, Source
except ImportError:
    # 如果导入失败，定义本地枚举
    class Permissions:
        ALL_VISIBLE = 1
    class Status:
        ACTIVE = 1
    class Source:
        DISCOGS = 1

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
SOURCE_COLLECTION_NAME = 'release_new'  # 检测缺失ID的源表
TARGET_COLLECTION_NAME = 'release_copy'  # 保存新数据的目标表
NOT_FOUND_COLLECTION_NAME = 'release_404'

# API配置
API_BASE_URL = 'https://api.discogs.com/releases'
API_RATE_LIMIT = 1.0  # 1秒1次请求
API_TIMEOUT = 30  # 请求超时时间
MAX_RETRIES = 3  # 最大重试次数

# 代理配置
PROXY_CONFIG = {
    'http': 'http://127.0.0.1:65502',
    'https': 'http://127.0.0.1:65502',
    # 'socks5': 'socks5://127.0.0.1:65502'  # 如果需要SOCKS5代理，取消注释
}

# 浏览器User-Agent池
USER_AGENT_POOL = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0"
]

# 处理配置
BATCH_SIZE = int(os.getenv('BATCH_SIZE', '100'))  # 批量插入大小
MAX_RECORDS = int(os.getenv('MAX_RECORDS', '0'))  # 最大处理记录数，0表示全部
START_ID = int(os.getenv('START_ID', '1'))  # 开始处理的ID
TEST_MODE = os.getenv('TEST_MODE', 'false').lower() == 'true'  # 测试模式

# 分阶段处理配置
MAX_SEGMENTS = int(os.getenv('MAX_SEGMENTS', '10'))  # 最大段数（优先使用）
SEGMENT_SIZE = int(os.getenv('SEGMENT_SIZE', '100000'))  # 每段检测的ID数量（备选方案）
PROCESSING_BATCH_SIZE = int(os.getenv('PROCESSING_BATCH_SIZE', '5000'))  # 每批次处理的缺失ID数量
MAX_CONCURRENT_BATCHES = int(os.getenv('MAX_CONCURRENT_BATCHES', '1'))  # 最大并发批次数

# 文件路径
PROGRESS_FILE = 'api_补全_progress.json'
LOG_FILE = 'api_补全_log.txt'
ERROR_LOG_FILE = 'api_补全_errors.txt'

# 分阶段处理文件路径
MISSING_IDS_FILE = 'missing_ids_segments.json'
BATCH_CONFIG_FILE = 'batch_config.json'
BATCH_PROGRESS_DIR = 'batch_progress'
SEGMENT_PROGRESS_FILE = 'segment_progress.json'

# 404 ID管理文件路径
NOT_FOUND_IDS_FILE = 'api_404_ids.json'

# 全局变量
should_stop = False
not_found_ids_set = set()  # 存储404 ID的集合

def signal_handler(signum, frame):
    """信号处理器，用于优雅停止"""
    global should_stop
    should_stop = True
    print("\n🛑 接收到停止信号，正在安全退出...")

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self):
        self.processed = 0
        self.successful = 0
        self.skipped_404 = 0
        self.rate_limited_429 = 0  # 新增429状态计数
        self.errors = 0
        self.start_time = time.time()
        self.last_id = START_ID
        self.load_progress()
    
    def load_progress(self):
        """加载进度"""
        if os.path.exists(PROGRESS_FILE):
            try:
                with open(PROGRESS_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.processed = data.get('processed', 0)
                    self.successful = data.get('successful', 0)
                    self.skipped_404 = data.get('skipped_404', 0)
                    self.errors = data.get('errors', 0)
                    self.last_id = data.get('last_id', START_ID)
                    logger.info(f"📂 加载进度: 已处理 {self.processed}, 成功 {self.successful}, 跳过 {self.skipped_404}, 错误 {self.errors}")
                    logger.info(f"📍 从ID {self.last_id} 继续处理")
            except Exception as e:
                logger.warning(f"⚠️ 加载进度失败: {e}")
    
    def save_progress(self):
        """保存进度"""
        try:
            data = {
                'processed': self.processed,
                'successful': self.successful,
                'skipped_404': self.skipped_404,
                'rate_limited_429': self.rate_limited_429,  # 新增429状态保存
                'errors': self.errors,
                'last_id': self.last_id,
                'timestamp': datetime.now().isoformat()
            }
            with open(PROGRESS_FILE, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"❌ 保存进度失败: {e}")
    
    def update(self, result_type: str, release_id: int):
        """更新进度"""
        self.processed += 1
        self.last_id = release_id
        
        if result_type == 'success':
            self.successful += 1
        elif result_type == '404':
            self.skipped_404 += 1
        elif result_type == '429':
            self.rate_limited_429 += 1  # 新增429状态计数
        elif result_type == 'error':
            self.errors += 1
        
        # 每处理100条记录保存一次进度
        if self.processed % 100 == 0:
            self.save_progress()
    
    def get_stats(self) -> Dict:
        """获取统计信息"""
        elapsed = time.time() - self.start_time
        rate = self.processed / elapsed if elapsed > 0 else 0
        
        return {
            'processed': self.processed,
            'successful': self.successful,
            'skipped_404': self.skipped_404,
            'errors': self.errors,
            'elapsed_time': elapsed,
            'processing_rate': rate,
            'success_rate': (self.successful / self.processed * 100) if self.processed > 0 else 0
        }

class DiscogsAPIClient:
    """Discogs API客户端"""

    def __init__(self, use_proxy=True):
        self.use_proxy = use_proxy
        self.session = requests.Session()
        self._setup_session()
        self.last_request_time = 0

    def _setup_session(self):
        """设置会话配置"""
        # 设置基础头信息
        headers = {
            'User-Agent': 'DiscogsReleaseCompleter/1.0 +https://example.com/contact',
            'Accept': 'application/vnd.discogs.v2.html+json',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

        # 随机选择一个浏览器User-Agent作为备选
        if random.random() < 0.3:  # 30%的概率使用浏览器User-Agent
            headers['User-Agent'] = random.choice(USER_AGENT_POOL)

        self.session.headers.update(headers)

        # 设置代理
        if self.use_proxy:
            self.session.proxies.update(PROXY_CONFIG)
    
    def _wait_for_rate_limit(self):
        """等待满足API频率限制"""
        elapsed = time.time() - self.last_request_time
        if elapsed < API_RATE_LIMIT:
            time.sleep(API_RATE_LIMIT - elapsed)
    
    def get_release(self, release_id: int) -> Optional[Dict]:
        """获取release数据"""
        self._wait_for_rate_limit()
        
        url = f"{API_BASE_URL}/{release_id}"
        
        for attempt in range(MAX_RETRIES):
            try:
                self.last_request_time = time.time()
                response = self.session.get(url, timeout=API_TIMEOUT)
                
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 404:
                    return None  # ID不存在
                elif response.status_code == 429:
                    # 频率限制，直接返回429标识，不重试
                    logger.info(f"⏳ API频率限制 (429) - ID {release_id}，记录状态并继续")
                    return "429"  # 返回特殊标识表示429状态
                else:
                    logger.warning(f"⚠️ API请求失败 (ID: {release_id}): HTTP {response.status_code}")
                    
            except requests.exceptions.Timeout:
                logger.warning(f"⏰ API请求超时 (ID: {release_id}), 尝试 {attempt + 1}/{MAX_RETRIES}")
            except requests.exceptions.RequestException as e:
                logger.warning(f"🌐 网络错误 (ID: {release_id}): {e}, 尝试 {attempt + 1}/{MAX_RETRIES}")
            
            # 移除指数退避等待，保持固定1秒间隔
        
        return False  # 表示请求失败

def connect_to_mongodb():
    """连接到MongoDB"""
    try:
        client = MongoClient(MONGO_URI)
        client.admin.command('ping')
        db = client[DB_NAME]

        # 确保目标集合存在
        if TARGET_COLLECTION_NAME not in db.list_collection_names():
            db.create_collection(TARGET_COLLECTION_NAME)
            logger.info(f"✅ 创建集合: {TARGET_COLLECTION_NAME}")

        # 确保release_404集合存在
        if NOT_FOUND_COLLECTION_NAME not in db.list_collection_names():
            db.create_collection(NOT_FOUND_COLLECTION_NAME)
            logger.info(f"✅ 创建集合: {NOT_FOUND_COLLECTION_NAME}")

        logger.info(f"✅ 成功连接到MongoDB - 源表: {DB_NAME}.{SOURCE_COLLECTION_NAME}")
        logger.info(f"✅ 目标表: {DB_NAME}.{TARGET_COLLECTION_NAME}")
        logger.info(f"✅ 404数据库表: {DB_NAME}.{NOT_FOUND_COLLECTION_NAME}")
        return client, db
    except Exception as e:
        logger.error(f"❌ MongoDB连接失败: {e}")
        raise

def load_404_ids(db):
    """从数据库加载404 ID列表到内存"""
    global not_found_ids_set
    try:
        collection = db[NOT_FOUND_COLLECTION_NAME]
        # 获取所有404 ID
        cursor = collection.find({}, {'id': 1, '_id': 0})
        not_found_ids_set = set(doc['id'] for doc in cursor)
        logger.info(f"📂 从数据库加载404 ID列表: {len(not_found_ids_set):,} 个ID")
        return len(not_found_ids_set)
    except Exception as e:
        logger.warning(f"⚠️ 从数据库加载404 ID失败: {e}")
        not_found_ids_set = set()
        return 0

def save_404_ids(db):
    """保存404 ID列表到数据库（批量插入新的404 ID）"""
    global not_found_ids_set
    try:
        collection = db[NOT_FOUND_COLLECTION_NAME]

        # 获取数据库中已存在的404 ID
        existing_ids = set(doc['id'] for doc in collection.find({}, {'id': 1, '_id': 0}))

        # 找出需要插入的新404 ID
        new_404_ids = not_found_ids_set - existing_ids

        if new_404_ids:
            # 批量插入新的404 ID
            docs_to_insert = [
                {
                    'id': release_id,
                    'created_at': datetime.now(),
                    'source': 'api_补全器'
                }
                for release_id in new_404_ids
            ]

            collection.insert_many(docs_to_insert, ordered=False)
            logger.info(f"💾 已保存新的404 ID到数据库: {len(new_404_ids):,} 个ID")
        else:
            logger.debug("📝 没有新的404 ID需要保存")

    except Exception as e:
        logger.error(f"❌ 保存404 ID到数据库失败: {e}")

def add_404_id(release_id: int, db=None, save_immediately: bool = False):
    """添加404 ID到集合"""
    global not_found_ids_set
    if release_id not in not_found_ids_set:
        not_found_ids_set.add(release_id)
        logger.debug(f"📝 记录404 ID: {release_id}")

        if save_immediately and db is not None:
            save_404_ids(db)

def insert_404_id_to_database(db, release_id: int):
    """立即插入单个404 ID到数据库"""
    try:
        collection = db[NOT_FOUND_COLLECTION_NAME]

        # 检查是否已存在
        if collection.find_one({'id': release_id}):
            logger.debug(f"📝 404 ID {release_id} 已存在于数据库")
            return True

        # 插入新的404 ID
        doc = {
            'id': release_id,
            'created_at': datetime.now(),
            'source': 'api_补全器'
        }
        collection.insert_one(doc)
        logger.debug(f"✅ 404 ID {release_id} 已插入数据库")
        return True

    except Exception as e:
        logger.error(f"❌ 插入404 ID {release_id} 到数据库失败: {e}")
        return False

def insert_429_record_to_database(db, release_id: int):
    """立即插入单个429状态记录到release_copy数据库"""
    try:
        target_collection = db[TARGET_COLLECTION_NAME]

        # 检查是否已存在
        if target_collection.find_one({'id': release_id}):
            logger.debug(f"📝 429 ID {release_id} 已存在于目标数据库")
            return True

        # 插入429状态记录（只包含id和status字段）
        doc = {
            'id': release_id,
            'status': 429,
            'created_at': datetime.now(),
            'source': 'api_补全器'
        }
        target_collection.insert_one(doc)
        logger.debug(f"✅ 429状态记录 {release_id} 已插入目标数据库")
        return True

    except Exception as e:
        logger.error(f"❌ 插入429状态记录 {release_id} 到数据库失败: {e}")
        return False

def get_404_ids_count() -> int:
    """获取404 ID数量"""
    global not_found_ids_set
    return len(not_found_ids_set)

def load_segment_progress():
    """加载分段检测进度"""
    if os.path.exists(SEGMENT_PROGRESS_FILE):
        try:
            with open(SEGMENT_PROGRESS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.warning(f"⚠️ 加载分段进度失败: {e}")
    return {'completed_segments': [], 'last_segment_start': START_ID}

def save_segment_progress(progress):
    """保存分段检测进度"""
    try:
        with open(SEGMENT_PROGRESS_FILE, 'w', encoding='utf-8') as f:
            json.dump(progress, f, indent=2, ensure_ascii=False)
    except Exception as e:
        logger.error(f"❌ 保存分段进度失败: {e}")

def detect_missing_ids_by_segments(db, start_id: int = START_ID, max_id: int = None) -> List[int]:
    """分段检测缺失的release ID（从release_new表检测）"""
    logger.info("🔍 开始分段检测缺失的release ID...")
    logger.info(f"📊 数据源表: {SOURCE_COLLECTION_NAME}")
    start_time = time.time()

    source_collection = db[SOURCE_COLLECTION_NAME]

    # 获取数据库中的最大ID
    if max_id is None:
        try:
            max_id_doc = list(source_collection.find({}, {'id': 1}).sort('id', -1).limit(1))
            max_id = int(max_id_doc[0]['id']) if max_id_doc else 34419592
            logger.info(f"📊 数据库中最大ID: {max_id}")
        except Exception as e:
            logger.error(f"❌ 获取最大ID失败: {e}")
            max_id = 34419592  # 使用默认值

    # 加载进度
    progress = load_segment_progress()
    completed_segments = set(progress.get('completed_segments', []))

    # 加载已检测的缺失ID
    missing_ids = []
    if os.path.exists(MISSING_IDS_FILE):
        try:
            with open(MISSING_IDS_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                missing_ids = data.get('missing_ids', [])
                logger.info(f"📂 加载已检测的缺失ID: {len(missing_ids):,} 个")
        except Exception as e:
            logger.warning(f"⚠️ 加载缺失ID文件失败: {e}")

    # 计算段大小和段数
    total_ids = max_id - start_id + 1

    if MAX_SEGMENTS > 0:
        # 使用最大段数来计算每段大小
        total_segments = min(MAX_SEGMENTS, total_ids)
        segment_size = (total_ids + total_segments - 1) // total_segments  # 向上取整
        logger.info(f"📊 使用最大段数模式: {total_segments} 段, 每段约 {segment_size:,} 个ID")
    else:
        # 使用固定段大小
        segment_size = SEGMENT_SIZE
        total_segments = (total_ids + segment_size - 1) // segment_size
        logger.info(f"📊 使用固定段大小模式: {total_segments} 段, 每段 {segment_size:,} 个ID")

    logger.info(f"📊 总ID数量: {total_ids:,}, 分为 {total_segments} 段处理")

    # 分段检测
    for segment_idx in range(total_segments):
        segment_start = start_id + segment_idx * segment_size
        segment_end = min(segment_start + segment_size - 1, max_id)

        # 跳过已完成的段
        if segment_idx in completed_segments:
            logger.info(f"⏭️ 跳过已完成段 {segment_idx + 1}/{total_segments} (ID: {segment_start}-{segment_end})")
            continue

        logger.info(f"🔄 检测段 {segment_idx + 1}/{total_segments} (ID: {segment_start}-{segment_end})")

        try:
            # 获取该段的现有ID
            query = {'id': {'$gte': segment_start, '$lte': segment_end}}
            cursor = source_collection.find(query, {'id': 1, '_id': 0})
            existing_ids = {int(doc['id']) for doc in cursor}

            # 计算该段的缺失ID
            segment_all_ids = set(range(segment_start, segment_end + 1))
            segment_missing = segment_all_ids - existing_ids

            # 添加到总缺失ID列表
            missing_ids.extend(sorted(segment_missing))

            # 标记段为已完成
            completed_segments.add(segment_idx)

            logger.info(f"✅ 段 {segment_idx + 1} 完成: 缺失 {len(segment_missing):,} 个ID")

            # 保存进度
            progress['completed_segments'] = list(completed_segments)
            progress['last_segment_start'] = segment_start
            save_segment_progress(progress)

            # 定期保存缺失ID
            if segment_idx % 10 == 0 or segment_idx == total_segments - 1:
                save_missing_ids_to_file(missing_ids, max_id)

        except Exception as e:
            logger.error(f"❌ 检测段 {segment_idx + 1} 失败: {e}")
            continue

    # 排除已知的404 ID
    global not_found_ids_set
    original_count = len(missing_ids)
    missing_ids = [id for id in missing_ids if id not in not_found_ids_set]
    excluded_count = original_count - len(missing_ids)

    if excluded_count > 0:
        logger.info(f"🚫 排除已知404 ID: {excluded_count:,} 个")

    # 最终保存
    save_missing_ids_to_file(missing_ids, max_id)

    elapsed = time.time() - start_time
    logger.info(f"✅ 分段检测完成，耗时: {elapsed:.2f} 秒")
    logger.info(f"📊 总缺失ID数量: {len(missing_ids):,}")

    return missing_ids

def save_missing_ids_to_file(missing_ids: List[int], max_id: int):
    """保存缺失ID到文件"""
    try:
        data = {
            'missing_ids': missing_ids,
            'total_count': len(missing_ids),
            'max_id': max_id,
            'generated_at': datetime.now().isoformat(),
            'segment_size': SEGMENT_SIZE
        }
        with open(MISSING_IDS_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        logger.info(f"💾 已保存 {len(missing_ids):,} 个缺失ID到文件")
    except Exception as e:
        logger.error(f"❌ 保存缺失ID文件失败: {e}")

def find_missing_ids(db, start_id: int = START_ID, max_id: int = None) -> Set[int]:
    """查找缺失的release ID（从release_new表检测）"""
    logger.info("🔍 开始检测缺失的release ID...")
    logger.info(f"📊 数据源表: {SOURCE_COLLECTION_NAME}")
    start_time = time.time()

    source_collection = db[SOURCE_COLLECTION_NAME]
    
    # 获取数据库中的最大ID
    if max_id is None:
        try:
            max_id_doc = list(collection.find({}, {'id': 1}).sort('id', -1).limit(1))
            max_id = max_id_doc[0]['id'] if max_id_doc else 34419592
            logger.info(f"📊 数据库中最大ID: {max_id}")
        except Exception as e:
            logger.error(f"❌ 获取最大ID失败: {e}")
            max_id = 34419592  # 使用默认值
    
    # 获取现有的ID集合
    logger.info(f"📥 获取现有ID集合 (范围: {start_id} - {max_id})...")
    existing_ids = set()
    
    # 使用批量查询优化性能
    batch_size = 10000
    for i in range(start_id, max_id + 1, batch_size):
        end_id = min(i + batch_size - 1, max_id)
        query = {'id': {'$gte': i, '$lte': end_id}}
        cursor = source_collection.find(query, {'id': 1, '_id': 0})
        
        batch_ids = {doc['id'] for doc in cursor}
        existing_ids.update(batch_ids)
        
        if len(existing_ids) % 100000 == 0:
            logger.info(f"🔄 已获取 {len(existing_ids):,} 个现有ID...")
    
    # 计算缺失的ID
    all_ids = set(range(start_id, max_id + 1))
    missing_ids = all_ids - existing_ids

    # 排除已知的404 ID
    global not_found_ids_set
    original_count = len(missing_ids)
    missing_ids = missing_ids - not_found_ids_set
    excluded_count = original_count - len(missing_ids)

    if excluded_count > 0:
        logger.info(f"🚫 排除已知404 ID: {excluded_count:,} 个")

    elapsed = time.time() - start_time
    logger.info(f"✅ 缺失ID检测完成，耗时: {elapsed:.2f} 秒")
    logger.info(f"📊 总ID范围: {len(all_ids):,}")
    logger.info(f"📊 现有ID数量: {len(existing_ids):,}")
    logger.info(f"📊 缺失ID数量: {len(missing_ids):,}")

    return missing_ids

def create_processing_batches(missing_ids: List[int]) -> List[Dict]:
    """将缺失ID分割成处理批次"""
    logger.info(f"📦 开始创建处理批次，总ID数: {len(missing_ids):,}")

    batches = []
    total_ids = len(missing_ids)

    for i in range(0, total_ids, PROCESSING_BATCH_SIZE):
        batch_end = min(i + PROCESSING_BATCH_SIZE, total_ids)
        batch_ids = missing_ids[i:batch_end]

        batch_info = {
            'batch_id': len(batches) + 1,
            'ids': batch_ids,
            'start_id': batch_ids[0],
            'end_id': batch_ids[-1],
            'count': len(batch_ids),
            'status': 'pending',
            'created_at': datetime.now().isoformat(),
            'progress': {
                'processed': 0,
                'successful': 0,
                'skipped_404': 0,
                'errors': 0
            }
        }
        batches.append(batch_info)

    logger.info(f"✅ 创建了 {len(batches)} 个批次，每批次最多 {PROCESSING_BATCH_SIZE} 个ID")

    # 保存批次配置
    save_batch_config(batches)

    return batches

def save_batch_config(batches: List[Dict]):
    """保存批次配置到文件"""
    try:
        config = {
            'batches': batches,
            'total_batches': len(batches),
            'batch_size': PROCESSING_BATCH_SIZE,
            'created_at': datetime.now().isoformat()
        }
        with open(BATCH_CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        logger.info(f"💾 已保存批次配置: {len(batches)} 个批次")
    except Exception as e:
        logger.error(f"❌ 保存批次配置失败: {e}")

def load_batch_config() -> List[Dict]:
    """加载批次配置"""
    if os.path.exists(BATCH_CONFIG_FILE):
        try:
            with open(BATCH_CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('batches', [])
        except Exception as e:
            logger.warning(f"⚠️ 加载批次配置失败: {e}")
    return []

def get_batch_progress_file(batch_id: int) -> str:
    """获取批次进度文件路径"""
    os.makedirs(BATCH_PROGRESS_DIR, exist_ok=True)
    return os.path.join(BATCH_PROGRESS_DIR, f'batch_{batch_id}_progress.json')

def save_batch_progress(batch_id: int, progress: Dict):
    """保存批次进度"""
    try:
        progress_file = get_batch_progress_file(batch_id)
        with open(progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress, f, indent=2, ensure_ascii=False)
    except Exception as e:
        logger.error(f"❌ 保存批次 {batch_id} 进度失败: {e}")

def load_batch_progress(batch_id: int) -> Dict:
    """加载批次进度"""
    progress_file = get_batch_progress_file(batch_id)
    if os.path.exists(progress_file):
        try:
            with open(progress_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.warning(f"⚠️ 加载批次 {batch_id} 进度失败: {e}")
    return {'processed': 0, 'successful': 0, 'skipped_404': 0, 'errors': 0, 'last_processed_id': None}

def safe_string_value(value, default=''):
    """安全地处理字符串值，确保JSON和CSV兼容性"""
    if value is None:
        return default

    # 转换为字符串
    str_value = str(value).strip()

    # 如果是空字符串，返回默认值
    if not str_value:
        return default

    # 移除或替换可能导致JSON解析问题的字符
    import re

    # 移除所有控制字符，包括换行符、制表符等
    # 这是最严格的处理，确保CSV兼容性
    str_value = re.sub(r'[\x00-\x1F\x7F]', '', str_value)

    # 额外处理常见的换行符组合，确保完全移除
    str_value = str_value.replace('\r\n', ' ').replace('\r', ' ').replace('\n', ' ')
    str_value = str_value.replace('\t', ' ')

    # 移除可能破坏JSON结构的特殊字符组合
    # 移除尾随的JSON语法字符（这些可能是数据污染）
    str_value = re.sub(r'[,}\]"]+$', '', str_value)
    str_value = re.sub(r'^[,{\["]+', '', str_value)

    # 处理连续的空格
    str_value = re.sub(r'\s+', ' ', str_value)

    # 再次检查是否为空
    str_value = str_value.strip()
    if not str_value:
        return default

    # json.dumps会自动处理转义，所以这里不需要手动转义
    return str_value

def safe_integer_value(value, default=None):
    """安全地处理整数值"""
    if value is None:
        return default

    try:
        return int(value)
    except (ValueError, TypeError):
        return default

def get_release_table_by_id(db, release_id):
    """从release表中获取images字段"""
    try:
        # 将release_id转换为string类型进行查询
        release_id_str = str(release_id)
        release_doc = db.release.find_one({'id': release_id_str})
        if release_doc:
            return {
                'images': release_doc.get('images', [])
            }
        return {
            'images': []
        }
    except Exception as e:
        logger.error(f"获取release表数据失败 (release_id: {release_id}): {e}")
        return {
            'images': []
        }

def convert_api_response_to_document(api_data: Dict, db) -> Dict:
    """将API响应转换为数据库文档格式（保持原始ID）"""
    try:
        # 从release表获取images字段
        release_id = api_data.get('id')
        logger.debug(f"🔍 获取release_id: {release_id}")
        db_fields = get_release_table_by_id(db, release_id)
        logger.debug(f"🔍 db_fields类型: {type(db_fields)}, 内容: {db_fields}")
        # 基础字段（保持原始ID，不重新编号）
        doc = {
            'id': safe_integer_value(api_data.get('id')),
            'title': safe_string_value(api_data.get('title'), ''),
            'country': safe_string_value(api_data.get('country'), ''),
            'master_id': safe_integer_value(api_data.get('master_id')),

            # 系统字段
            'images_permissions': Permissions.ALL_VISIBLE.value,
            'delete_status': Status.ACTIVE.value,
            'permissions': Permissions.ALL_VISIBLE.value,
            'source': Source.DISCOGS.value,
            'created_at': datetime.now(timezone.utc),
            'updated_at': datetime.now(timezone.utc)
        }

        # 处理艺术家 (artists)
        artists = []
        if 'artists' in api_data:
            for artist in api_data['artists']:
                artist_doc = {
                    'artist_id': safe_integer_value(artist.get('id')),
                    'name': safe_string_value(artist.get('name'), ''),
                    'role': safe_string_value(artist.get('role'), 'Primary')
                }
                # 只在anv有有效值时才添加该字段
                anv_value = safe_string_value(artist.get('anv'))
                if anv_value:  # 只有非空字符串才添加
                    artist_doc['anv'] = anv_value
                artists.append(artist_doc)
        doc['artists'] = artists

        # 处理额外艺术家 (extraartists)
        extra_artists = []
        # 支持两种字段名：extraartists (API) 和 extra_artists (测试数据)
        extra_artists_data = api_data.get('extraartists') or api_data.get('extra_artists', [])
        if extra_artists_data:
            for artist in extra_artists_data:
                artist_doc = {
                    'artist_id': safe_integer_value(artist.get('id')),
                    'name': safe_string_value(artist.get('name'), ''),
                    'role': safe_string_value(artist.get('role'), 'Unknown')
                }
                # 只在anv有有效值时才添加该字段
                anv_value = safe_string_value(artist.get('anv'))
                if anv_value:  # 只有非空字符串才添加
                    artist_doc['anv'] = anv_value
                extra_artists.append(artist_doc)
        doc['extra_artists'] = extra_artists

        # 处理标签 (labels)
        labels = []
        if 'labels' in api_data:
            for label in api_data['labels']:
                labels.append({
                    'name': label.get('name', ''),
                    'catno': label.get('catno', ''),
                    'id': str(label.get('id', ''))
                })
        doc['labels'] = labels

        # 处理公司 (companies)
        companies = []
        if 'companies' in api_data:
            logger.debug(f"🔍 处理companies字段: {api_data['companies']}")
            for company in api_data['companies']:
                logger.debug(f"🔍 处理单个company: {company}, 类型: {type(company)}")
                if isinstance(company, str):
                    # 如果company是字符串，直接使用
                    company_doc = {'name': company}
                elif isinstance(company, dict):
                    # 如果company是字典，使用get方法
                    company_doc = {'name': company.get('name', '')}
                    if 'id' in company:
                        company_doc['id'] = str(company['id'])
                    if 'entity_type' in company:
                        company_doc['entity_type'] = company['entity_type']
                    if 'entity_type_name' in company:
                        company_doc['entity_type_name'] = company['entity_type_name']
                    if 'resource_url' in company:
                        company_doc['resource_url'] = company['resource_url']
                else:
                    logger.warning(f"⚠️ 未知的company类型: {type(company)}")
                    continue
                companies.append(company_doc)
        doc['companies'] = companies

        # 处理格式 (formats)
        formats = []
        if 'formats' in api_data:
            for format_item in api_data['formats']:
                format_doc = {
                    'name': format_item.get('name', ''),
                    'qty': str(format_item.get('qty', '')),
                    'text': format_item.get('text', ''),
                    'descriptions': format_item.get('descriptions', [])
                }
                formats.append(format_doc)
        doc['formats'] = formats

        # 处理简单列表字段
        doc['genres'] = api_data.get('genres', [])
        doc['styles'] = api_data.get('styles', [])

        # 处理标识符 (identifiers)
        identifiers = []
        if 'identifiers' in api_data:
            for identifier in api_data['identifiers']:
                identifiers.append({
                    'type': identifier.get('type', ''),
                    'value': identifier.get('value', ''),
                    'description': identifier.get('description', '')
                })
        doc['identifiers'] = identifiers

        # 处理曲目列表 (tracklist)
        tracklist = []
        if 'tracklist' in api_data:
            for track in api_data['tracklist']:
                track_doc = {
                    'position': track.get('position', ''),
                    'title': track.get('title', '')
                }
                if 'duration' in track:
                    track_doc['duration'] = track['duration']
                tracklist.append(track_doc)
        doc['tracklist'] = tracklist

        # 处理其他字段
        doc['images'] = db_fields['images']  # 从数据库获取
        doc['notes'] = safe_string_value(api_data.get('notes'), '')
        doc['year'] = safe_integer_value(api_data.get('year'))
        doc['discogs_status'] = safe_string_value(api_data.get('status'), 'unknown')

        return doc

    except Exception as e:
        logger.error(f"❌ 数据转换失败: {e}")
        return None

def test_artists_json_format():
    """测试artists字段的JSON格式生成"""
    # 模拟有问题的数据
    test_cases = [
        {
            'id': 16339972,
            'name': 'Ghetto Prophets (3)',
            'role': '',  # 空字符串
            'anv': ''    # 空字符串
        },
        {
            'id': 1427641,
            'name': 'Bosque Brown',
            'role': '"quoted"',  # 包含引号
            'anv': 'test'
        },
        {
            'id': 123,
            'name': 'Test Artist',
            'role': 'Primary',
            'anv': None  # None值
        }
    ]

    print("🧪 测试artists字段JSON格式生成...")
    for i, test_case in enumerate(test_cases):
        artist_doc = {
            'artist_id': safe_integer_value(test_case.get('id')),
            'name': safe_string_value(test_case.get('name'), ''),
            'role': safe_string_value(test_case.get('role'), 'Primary')
        }
        anv_value = safe_string_value(test_case.get('anv'))
        if anv_value:
            artist_doc['anv'] = anv_value

        artists = [artist_doc]
        json_result = format_array_for_csv(artists)
        print(f"测试用例 {i+1}: {json_result}")

        # 验证JSON有效性
        try:
            json.loads(json_result)
            print(f"✅ 测试用例 {i+1} JSON有效")
        except json.JSONDecodeError as e:
            print(f"❌ 测试用例 {i+1} JSON无效: {e}")

def format_array_for_csv(data_list):
    """将列表数据格式化为JSON数组格式，用于CSV存储，确保CSV兼容性"""
    if not data_list:
        return "[]"

    try:
        # 预处理数据，确保所有字符串字段都经过安全处理
        cleaned_data = clean_data_for_json(data_list)

        if isinstance(cleaned_data, list):
            # 确保列表中的每个元素都是JSON可序列化的
            # 使用紧凑格式，无空格和换行符，确保CSV兼容性
            json_str = json.dumps(cleaned_data, ensure_ascii=False, separators=(',', ':'))
        else:
            json_str = json.dumps([cleaned_data], ensure_ascii=False, separators=(',', ':'))

        # 额外的CSV安全处理：确保没有换行符或其他控制字符
        json_str = json_str.replace('\n', '').replace('\r', '').replace('\t', ' ')

        # 验证生成的JSON是否有效
        json.loads(json_str)  # 这会抛出异常如果JSON无效
        return json_str

    except (TypeError, ValueError, json.JSONDecodeError) as e:
        logger.warning(f"JSON序列化失败，使用空数组: {e}")
        logger.warning(f"原始数据: {data_list}")
        return "[]"

def clean_data_for_json(data):
    """递归清理数据结构中的字符串，确保JSON兼容性"""
    if isinstance(data, dict):
        return {key: clean_data_for_json(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [clean_data_for_json(item) for item in data]
    elif isinstance(data, str):
        return safe_string_value(data)
    else:
        return data

def validate_csv_json_compatibility(csv_filename: str, sample_size: int = 100) -> Dict:
    """验证CSV文件中JSON字段的解析兼容性"""
    validation_result = {
        'total_rows': 0,
        'json_parse_errors': 0,
        'error_details': [],
        'success_rate': 0.0
    }

    if not os.path.exists(csv_filename):
        logger.warning(f"CSV文件不存在: {csv_filename}")
        return validation_result

    try:
        import pandas as pd

        # 读取CSV文件
        df = pd.read_csv(csv_filename, nrows=sample_size)
        validation_result['total_rows'] = len(df)

        # 定义需要验证的JSON字段
        json_fields = ['artists', 'extra_artists', 'labels', 'companies',
                      'formats', 'genres', 'styles', 'identifiers', 'tracklist', 'images']

        for index, row in df.iterrows():
            for field in json_fields:
                if field in row and pd.notna(row[field]):
                    try:
                        json.loads(str(row[field]))
                    except json.JSONDecodeError as e:
                        validation_result['json_parse_errors'] += 1
                        validation_result['error_details'].append({
                            'row': index + 1,
                            'field': field,
                            'error': str(e),
                            'value': str(row[field])[:100] + '...' if len(str(row[field])) > 100 else str(row[field])
                        })

        # 计算成功率
        total_json_fields = validation_result['total_rows'] * len(json_fields)
        if total_json_fields > 0:
            validation_result['success_rate'] = ((total_json_fields - validation_result['json_parse_errors']) / total_json_fields) * 100

        logger.info(f"📊 CSV验证完成: {validation_result['total_rows']} 行, "
                   f"JSON解析错误: {validation_result['json_parse_errors']}, "
                   f"成功率: {validation_result['success_rate']:.2f}%")

        return validation_result

    except Exception as e:
        logger.error(f"❌ CSV验证失败: {e}")
        return validation_result

def write_releases_to_csv(release_docs: List[Dict], csv_filename: str, append_mode: bool = True):
    """将release数据写入CSV文件，确保CSV和JSON解析兼容性"""
    if not release_docs:
        logger.info("没有数据需要写入CSV文件")
        return

    # 定义CSV字段（基于process_releases.py的字段结构）
    fieldnames = [
        'id', 'y_id', 'title', 'artists', 'extra_artists', 'labels', 'companies',
        'country', 'formats', 'genres', 'styles', 'identifiers', 'tracklist',
        'master_id', 'discogs_status', 'images', 'notes', 'year',
        'images_permissions', 'permissions', 'source', 'created_at', 'updated_at'
    ]

    try:
        # 检查文件是否存在以决定是否写入表头
        file_exists = os.path.exists(csv_filename)
        mode = 'a' if append_mode and file_exists else 'w'

        with open(csv_filename, mode, newline='', encoding='utf-8') as csvfile:
            # 配置CSV写入器，使用QUOTE_MINIMAL避免JSON字符串内部的引号被过度转义
            writer = csv.DictWriter(
                csvfile,
                fieldnames=fieldnames,
                quoting=csv.QUOTE_MINIMAL,  # 只在必要时加引号
                doublequote=True,           # 使用双引号转义
                lineterminator='\n'        # 明确指定行终止符
            )

            # 只在新文件或覆盖模式时写入表头
            if not file_exists or not append_mode:
                writer.writeheader()

            # 写入数据
            for doc in release_docs:
                # 格式化数组字段
                csv_row = {}
                for field in fieldnames:
                    value = doc.get(field, '')

                    # 处理数组字段
                    if field in ['artists', 'extra_artists', 'labels', 'companies',
                               'formats', 'genres', 'styles', 'identifiers',
                               'tracklist', 'images']:
                        csv_row[field] = format_array_for_csv(value)
                    # 处理日期字段
                    elif field in ['created_at', 'updated_at'] and isinstance(value, datetime):
                        csv_row[field] = value.isoformat()
                    # 处理字符串字段，确保安全
                    elif isinstance(value, str):
                        csv_row[field] = safe_string_value(value)
                    else:
                        csv_row[field] = value

                writer.writerow(csv_row)

        logger.info(f"✅ 成功写入 {len(release_docs)} 条记录到 {csv_filename}")

    except Exception as e:
        logger.error(f"❌ CSV写入失败: {e}")

def process_batch(batch_info: Dict, db, api_client: DiscogsAPIClient) -> Dict:
    """处理单个批次"""
    batch_id = batch_info['batch_id']
    batch_ids = batch_info['ids']

    logger.info(f"🚀 开始处理批次 {batch_id}: {len(batch_ids)} 个ID")
    logger.info(f"📊 ID范围: {batch_ids[0]} - {batch_ids[-1]}")

    # 加载批次进度
    progress = load_batch_progress(batch_id)
    last_processed_id = progress.get('last_processed_id')

    # 确定开始位置
    start_idx = 0
    if last_processed_id:
        try:
            start_idx = batch_ids.index(last_processed_id) + 1
            logger.info(f"📍 从ID {last_processed_id} 之后继续处理")
        except ValueError:
            logger.warning(f"⚠️ 未找到上次处理的ID {last_processed_id}，从头开始")

    # 不再需要y_id计数器，保持原始ID

    batch_docs = []

    for i in range(start_idx, len(batch_ids)):
        if should_stop:
            logger.info(f"🛑 批次 {batch_id} 收到停止信号")
            break

        release_id = batch_ids[i]

        try:
            # API调用
            api_data = api_client.get_release(release_id)

            if api_data is None:
                # 404 - ID不存在
                add_404_id(release_id, db)  # 记录404 ID
                insert_404_id_to_database(db, release_id)  # 立即插入到数据库
                progress['skipped_404'] += 1
                logger.debug(f"⏭️ 批次 {batch_id} - ID {release_id} 不存在 (404)")

            elif api_data == "429":
                # 429 - 频率限制
                insert_429_record_to_database(db, release_id)  # 立即插入429状态记录
                progress['rate_limited_429'] = progress.get('rate_limited_429', 0) + 1
                logger.debug(f"⏳ 批次 {batch_id} - ID {release_id} 频率限制 (429)")

            elif api_data is False:
                # API请求失败
                progress['errors'] += 1
                logger.warning(f"❌ 批次 {batch_id} - ID {release_id} API请求失败")

            else:
                # 成功获取数据
                doc = convert_api_response_to_document(api_data, db)

                if doc:
                    batch_docs.append(doc)
                    progress['successful'] += 1
                    logger.debug(f"✅ 批次 {batch_id} - ID {release_id} 处理成功")

                    # 批量插入数据库
                    if len(batch_docs) >= BATCH_SIZE:
                        success_count, error_count = batch_insert_to_database(db, batch_docs)
                        if error_count > 0:
                            progress['errors'] += error_count
                        batch_docs = []
                else:
                    progress['errors'] += 1
                    logger.warning(f"❌ 批次 {batch_id} - ID {release_id} 数据转换失败")

            progress['processed'] += 1
            progress['last_processed_id'] = release_id

            # 定期保存进度
            if progress['processed'] % 100 == 0:
                save_batch_progress(batch_id, progress)
                logger.info(f"📊 批次 {batch_id} 进度: {progress['processed']}/{len(batch_ids)} "
                          f"(成功: {progress['successful']}, 404: {progress['skipped_404']}, "
                          f"429: {progress.get('rate_limited_429', 0)}, 错误: {progress['errors']})")

        except Exception as e:
            progress['errors'] += 1
            logger.error(f"❌ 批次 {batch_id} - ID {release_id} 处理异常: {e}")

    # 插入剩余的文档到数据库
    if batch_docs:
        success_count, error_count = batch_insert_to_database(db, batch_docs)
        if error_count > 0:
            progress['errors'] += error_count

    # 保存最终进度
    progress['completed_at'] = datetime.now().isoformat()
    save_batch_progress(batch_id, progress)

    logger.info(f"✅ 批次 {batch_id} 处理完成")
    logger.info(f"📊 最终统计: 处理 {progress['processed']}, 成功 {progress['successful']}, "
              f"404跳过 {progress['skipped_404']}, 429限制 {progress.get('rate_limited_429', 0)}, "
              f"错误 {progress['errors']}")

    return progress

def process_missing_releases(missing_ids: List[int], db, api_client: DiscogsAPIClient, progress: ProgressTracker):
    """处理缺失的release记录"""
    target_collection = db[TARGET_COLLECTION_NAME]
    batch_docs = []

    # 不再需要y_id计数器，保持原始ID

    for release_id in missing_ids:
        if should_stop:
            logger.info("🛑 接收到停止信号，正在保存当前批次...")
            break

        try:
            # 调用API获取数据
            api_data = api_client.get_release(release_id)

            if api_data is None:
                # 404 - ID不存在
                add_404_id(release_id, db)  # 记录404 ID
                insert_404_id_to_database(db, release_id)  # 立即插入到数据库
                progress.update('404', release_id)
                logger.debug(f"⏭️ ID {release_id} 不存在，跳过")
                continue
            elif api_data == "429":
                # 429 - 频率限制
                insert_429_record_to_database(db, release_id)  # 立即插入429状态记录
                progress.update('429', release_id)
                logger.debug(f"⏳ ID {release_id} 频率限制，记录状态")
                continue
            elif api_data is False:
                # API请求失败
                progress.update('error', release_id)
                logger.error(f"❌ API请求失败: ID {release_id}")
                continue

            # 转换为数据库文档
            doc = convert_api_response_to_document(api_data, db)

            if doc is None:
                progress.update('error', release_id)
                continue

            batch_docs.append(doc)
            progress.update('success', release_id)

            # 批量插入数据库
            if len(batch_docs) >= BATCH_SIZE:
                batch_insert_to_database(db, batch_docs)
                batch_docs = []

            # 显示进度
            if progress.processed % 100 == 0:
                stats = progress.get_stats()
                logger.info(f"🔄 进度: {progress.processed:,}/{len(missing_ids):,} "
                          f"(成功: {progress.successful:,}, 404: {progress.skipped_404:,}, "
                          f"错误: {progress.errors:,}, 速度: {stats['processing_rate']:.2f}/秒)")

        except Exception as e:
            logger.error(f"❌ 处理ID {release_id} 时发生错误: {e}")
            progress.update('error', release_id)

    # 插入剩余的文档到数据库
    if batch_docs:
        batch_insert_to_database(db, batch_docs)

    # 保存最终进度
    progress.save_progress()

# 移除get_next_yid_counter函数，因为不再重新编号，保持原始ID

def batch_insert_to_database(db, docs: List[Dict]) -> tuple:
    """批量插入文档到数据库"""
    if not docs:
        return 0, 0

    try:
        target_collection = db[TARGET_COLLECTION_NAME]
        result = target_collection.insert_many(docs, ordered=False)
        success_count = len(result.inserted_ids)
        logger.info(f"✅ 批量插入成功: {success_count} 条记录")
        return success_count, 0
    except Exception as e:
        logger.error(f"❌ 批量插入失败: {e}")
        # 尝试逐条插入
        success_count = 0
        error_count = 0
        for doc in docs:
            try:
                target_collection.insert_one(doc)
                success_count += 1
            except Exception as single_error:
                error_count += 1
                logger.error(f"❌ 单条插入失败 (ID: {doc.get('id')}): {single_error}")
        return success_count, error_count

def insert_single_document(db, doc: Dict) -> bool:
    """插入单个文档到数据库"""
    try:
        target_collection = db[TARGET_COLLECTION_NAME]
        target_collection.insert_one(doc)
        logger.debug(f"✅ 单条插入成功 (ID: {doc.get('id')})")
        return True
    except Exception as e:
        logger.error(f"❌ 单条插入失败 (ID: {doc.get('id')}): {e}")
        return False

def main_staged():
    """分阶段处理主函数"""
    logger.info("🚀 Discogs API Release 数据补全器启动 (分阶段数据库插入模式)")
    logger.info("=" * 60)
    if MAX_SEGMENTS > 0:
        logger.info(f"📊 最大段数: {MAX_SEGMENTS} (动态计算每段大小)")
    else:
        logger.info(f"📊 固定段大小: {SEGMENT_SIZE:,}")
    logger.info(f"📦 批次大小: {PROCESSING_BATCH_SIZE:,}")
    logger.info(f"📁 源数据库表: {SOURCE_COLLECTION_NAME}")
    logger.info(f"📁 目标数据库表: {TARGET_COLLECTION_NAME}")

    if TEST_MODE:
        logger.info("🧪 测试模式已启用")

    try:
        # 连接数据库
        client, db = connect_to_mongodb()

        # 加载404 ID列表
        initial_404_count = load_404_ids(db)
        logger.info(f"📊 已加载404 ID列表: {initial_404_count:,} 个")

        # 阶段1: 检测缺失ID
        logger.info("\n" + "=" * 60)
        logger.info("📋 阶段1: 检测缺失的release ID")
        logger.info("=" * 60)

        missing_ids = detect_missing_ids_by_segments(db)

        if not missing_ids:
            logger.info("✅ 没有发现缺失的ID，数据库已完整")
            return

        # 测试模式限制
        if TEST_MODE:
            missing_ids = missing_ids[:50]  # 测试模式只处理50个ID
            logger.info(f"🧪 测试模式：只处理前 {len(missing_ids)} 个ID")

        # 阶段2: 创建处理批次
        logger.info("\n" + "=" * 60)
        logger.info("📦 阶段2: 创建处理批次")
        logger.info("=" * 60)

        batches = create_processing_batches(missing_ids)

        # 阶段3: 分批处理
        logger.info("\n" + "=" * 60)
        logger.info("🔄 阶段3: 分批处理API调用")
        logger.info("=" * 60)

        api_client = DiscogsAPIClient(use_proxy=True)
        total_stats = {'processed': 0, 'successful': 0, 'skipped_404': 0, 'errors': 0}

        for batch_info in batches:
            if should_stop:
                logger.info("🛑 收到停止信号，终止处理")
                break

            batch_stats = process_batch(batch_info, db, api_client)

            # 累计统计
            for key in total_stats:
                total_stats[key] += batch_stats.get(key, 0)

            logger.info(f"📊 累计进度: 处理 {total_stats['processed']:,}, "
                      f"成功 {total_stats['successful']:,}, "
                      f"404跳过 {total_stats['skipped_404']:,}, "
                      f"错误 {total_stats['errors']:,}")

        # 输出最终统计
        logger.info("\n" + "=" * 60)
        logger.info("📊 最终统计报告")
        logger.info("=" * 60)
        logger.info(f"总处理数量: {total_stats['processed']:,}")
        logger.info(f"成功获取: {total_stats['successful']:,}")
        logger.info(f"404跳过: {total_stats['skipped_404']:,}")
        logger.info(f"错误数量: {total_stats['errors']:,}")

        if total_stats['processed'] > 0:
            success_rate = (total_stats['successful'] / total_stats['processed']) * 100
            logger.info(f"成功率: {success_rate:.2f}%")

        logger.info("=" * 60)
        logger.info(f"📊 最终404 ID数量: {get_404_ids_count():,}")

        client.close()

    except KeyboardInterrupt:
        logger.info("🛑 用户中断程序")
    except Exception as e:
        logger.error(f"❌ 处理过程中发生错误: {e}")
    finally:
        # 保存404 ID列表
        if 'db' in locals():
            save_404_ids(db)
        logger.info("🏁 程序结束")

def main():
    """主函数"""
    logger.info("🚀 Discogs API Release 数据补全器启动 (数据库插入模式)")
    logger.info("=" * 60)
    logger.info(f"📁 源数据库表: {SOURCE_COLLECTION_NAME}")
    logger.info(f"📁 目标数据库表: {TARGET_COLLECTION_NAME}")
    
    if TEST_MODE:
        logger.info("🧪 测试模式已启用")
    
    try:
        # 连接数据库
        client, db = connect_to_mongodb()

        # 加载404 ID列表
        initial_404_count = load_404_ids(db)
        logger.info(f"📊 已加载404 ID列表: {initial_404_count:,} 个")

        # 初始化组件
        progress = ProgressTracker()
        api_client = DiscogsAPIClient(use_proxy=True)
        
        # 查找缺失的ID
        missing_ids = find_missing_ids(db, progress.last_id)
        
        if not missing_ids:
            logger.info("✅ 没有发现缺失的ID，数据库已完整")
            return
        
        # 转换为排序列表
        missing_ids_list = sorted(list(missing_ids))
        
        # 测试模式只处理前几条
        if TEST_MODE:
            missing_ids_list = missing_ids_list[:10]
            logger.info(f"🧪 测试模式：只处理前 {len(missing_ids_list)} 条记录")
        
        # 限制处理数量
        if MAX_RECORDS > 0:
            missing_ids_list = missing_ids_list[:MAX_RECORDS]
            logger.info(f"📝 限制处理记录数: {len(missing_ids_list)}")
        
        logger.info(f"🎯 开始处理 {len(missing_ids_list):,} 个缺失的ID...")

        # 处理缺失的ID
        process_missing_releases(missing_ids_list, db, api_client, progress)

        # 输出最终统计
        final_stats = progress.get_stats()
        logger.info("\n" + "=" * 60)
        logger.info("📊 最终统计报告")
        logger.info("=" * 60)
        logger.info(f"总处理数量: {final_stats['processed']:,}")
        logger.info(f"成功获取: {final_stats['successful']:,}")
        logger.info(f"404跳过: {final_stats['skipped_404']:,}")
        logger.info(f"错误数量: {final_stats['errors']:,}")
        logger.info(f"成功率: {final_stats['success_rate']:.2f}%")
        logger.info(f"处理速度: {final_stats['processing_rate']:.2f} 条/秒")
        logger.info(f"总耗时: {final_stats['elapsed_time']:.2f} 秒")
        logger.info(f"📊 最终404 ID数量: {get_404_ids_count():,}")
        logger.info("=" * 60)

    except KeyboardInterrupt:
        logger.info("⏹️ 用户中断处理")
    except Exception as e:
        logger.error(f"❌ 处理过程中发生错误: {e}")
        raise
    finally:
        # 保存404 ID列表
        if 'db' in locals():
            save_404_ids(db)
        if 'client' in locals():
            client.close()
        logger.info("🏁 程序结束")

if __name__ == "__main__":
    # 检查是否运行测试
    test_mode = os.getenv('TEST_JSON', 'false').lower() == 'true'

    if test_mode:
        test_artists_json_format()
    else:
        # 检查是否使用分阶段处理模式
        staged_mode = os.getenv('STAGED_MODE', 'true').lower() == 'true'

        if staged_mode:
            main_staged()
        else:
            main()
