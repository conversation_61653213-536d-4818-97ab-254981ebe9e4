#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
环境检查脚本
在运行主程序前检查所有必要的依赖和配置
"""

import sys
import os
import importlib
import logging
from typing import List, Tuple

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_python_version() -> bool:
    """检查Python版本"""
    logger.info("🐍 检查Python版本...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        logger.error(f"❌ Python版本过低: {version.major}.{version.minor}")
        logger.error("   需要Python 3.7或更高版本")
        return False
    
    logger.info(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def check_required_packages() -> bool:
    """检查必需的Python包"""
    logger.info("📦 检查必需的Python包...")
    
    required_packages = [
        ('requests', '用于HTTP请求'),
        ('pymongo', '用于MongoDB连接'),
        ('argparse', '用于命令行参数解析'),
    ]
    
    missing_packages = []
    
    for package, description in required_packages:
        try:
            importlib.import_module(package)
            logger.info(f"✅ {package} - {description}")
        except ImportError:
            logger.error(f"❌ {package} - {description} (未安装)")
            missing_packages.append(package)
    
    if missing_packages:
        logger.error("缺少必需的包，请运行以下命令安装:")
        logger.error(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_main_script() -> bool:
    """检查主脚本文件"""
    logger.info("📄 检查主脚本文件...")
    
    script_path = "api_incremental_release_fetcher.py"
    if not os.path.exists(script_path):
        logger.error(f"❌ 主脚本文件不存在: {script_path}")
        return False
    
    logger.info(f"✅ 主脚本文件存在: {script_path}")
    return True

def check_database_connection() -> bool:
    """检查数据库连接"""
    logger.info("🗄️  检查数据库连接...")
    
    try:
        from pymongo import MongoClient
        
        # 使用默认配置尝试连接
        mongo_uri = os.getenv('MONGO_URI', '**********************************************************')
        
        client = MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
        # 尝试获取服务器信息
        client.server_info()
        client.close()
        
        logger.info("✅ 数据库连接正常")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        logger.error("   请检查数据库配置和网络连接")
        return False

def check_network_connectivity() -> bool:
    """检查网络连接"""
    logger.info("🌐 检查网络连接...")
    
    try:
        import requests
        
        # 测试Discogs API连接
        response = requests.get(
            "https://api.discogs.com/",
            timeout=10,
            headers={'User-Agent': 'EnvironmentChecker/1.0'}
        )
        
        if response.status_code == 200:
            logger.info("✅ Discogs API连接正常")
            return True
        else:
            logger.warning(f"⚠️  Discogs API响应异常: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 网络连接失败: {e}")
        logger.error("   请检查网络连接或代理设置")
        return False

def check_file_permissions() -> bool:
    """检查文件权限"""
    logger.info("🔐 检查文件权限...")
    
    # 检查当前目录写权限
    try:
        test_file = "test_write_permission.tmp"
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        logger.info("✅ 目录写权限正常")
        return True
    except Exception as e:
        logger.error(f"❌ 目录写权限检查失败: {e}")
        return False

def check_environment_variables() -> bool:
    """检查环境变量"""
    logger.info("🔧 检查环境变量...")
    
    # 检查可选的环境变量
    optional_vars = [
        ('MONGO_URI', '数据库连接URI'),
        ('DB_NAME', '数据库名称'),
        ('HTTP_PROXY', 'HTTP代理'),
        ('HTTPS_PROXY', 'HTTPS代理'),
    ]
    
    for var, description in optional_vars:
        value = os.getenv(var)
        if value:
            logger.info(f"✅ {var} = {value[:20]}... - {description}")
        else:
            logger.info(f"ℹ️  {var} (未设置) - {description}")
    
    return True

def main():
    """主检查函数"""
    logger.info("🚀 开始环境检查...")
    logger.info("=" * 50)
    
    checks = [
        ("Python版本", check_python_version),
        ("必需包", check_required_packages),
        ("主脚本", check_main_script),
        ("数据库连接", check_database_connection),
        ("网络连接", check_network_connectivity),
        ("文件权限", check_file_permissions),
        ("环境变量", check_environment_variables),
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        logger.info(f"\n📋 检查: {check_name}")
        logger.info("-" * 30)
        
        try:
            if check_func():
                passed += 1
            else:
                logger.error(f"检查失败: {check_name}")
        except Exception as e:
            logger.error(f"检查异常: {check_name} - {e}")
    
    logger.info("\n" + "=" * 50)
    logger.info("📊 环境检查结果")
    logger.info("=" * 50)
    logger.info(f"通过: {passed}/{total}")
    
    if passed == total:
        logger.info("🎉 所有检查通过！环境配置正常")
        logger.info("可以安全运行主程序")
        return True
    else:
        logger.error("❌ 部分检查失败，请修复问题后重试")
        logger.error("建议解决所有问题后再运行主程序")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
