@echo off
chcp 65001 >nul
echo ========================================
echo   Discogs API Release 数据获取器
echo ========================================
echo.

:: 设置脚本目录为当前工作目录
cd /d "%~dp0"

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python，请先安装Python
    pause
    exit /b 1
)

:: 检查主脚本是否存在
if not exist "api_incremental_release_fetcher.py" (
    echo ❌ 错误：未找到主脚本文件 api_incremental_release_fetcher.py
    pause
    exit /b 1
)

:: 显示菜单
echo 请选择运行模式：
echo.
echo 1. 测试模式 (处理少量数据)
echo 2. 生产模式 (处理所有数据)
echo 3. 自定义参数运行
echo 4. 查看帮助信息
echo 5. 退出
echo.
set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" goto test_mode
if "%choice%"=="2" goto production_mode
if "%choice%"=="3" goto custom_mode
if "%choice%"=="4" goto help_mode
if "%choice%"=="5" goto exit
echo 无效选择，请重新运行脚本
pause
exit /b 1

:test_mode
echo.
echo 🧪 启动测试模式...
echo 将处理少量数据用于测试
echo.
python api_incremental_release_fetcher.py
goto end

:production_mode
echo.
echo 🚀 启动生产模式...
echo ⚠️  警告：将处理大量数据，可能需要很长时间
echo.
set /p confirm="确认启动生产模式？(y/N): "
if /i not "%confirm%"=="y" (
    echo 已取消
    goto end
)
python api_incremental_release_fetcher.py --production
goto end

:custom_mode
echo.
echo 🔧 自定义参数模式
echo.
echo 可用参数：
echo   --no-proxy        不使用代理
echo   --production      生产模式
echo   --start-id NUM    指定起始ID
echo   --max-records NUM 最大处理记录数
echo.
set /p custom_args="请输入参数: "
python api_incremental_release_fetcher.py %custom_args%
goto end

:help_mode
echo.
echo 📖 帮助信息
python api_incremental_release_fetcher.py --help
goto end

:end
echo.
echo 程序执行完成
pause

:exit
echo 再见！
