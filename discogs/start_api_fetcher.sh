#!/bin/bash

# Discogs API Release 数据获取器启动脚本
# 支持 Linux/macOS 系统

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示标题
echo "========================================"
echo "   Discogs API Release 数据获取器"
echo "========================================"
echo

# 切换到脚本所在目录
cd "$(dirname "$0")"

# 检查Python是否安装
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    print_error "未找到Python，请先安装Python"
    exit 1
fi

# 确定Python命令
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
else
    PYTHON_CMD="python"
fi

print_info "使用Python命令: $PYTHON_CMD"

# 检查主脚本是否存在
if [ ! -f "api_incremental_release_fetcher.py" ]; then
    print_error "未找到主脚本文件 api_incremental_release_fetcher.py"
    exit 1
fi

# 显示菜单
show_menu() {
    echo "请选择运行模式："
    echo
    echo "1. 测试模式 (处理少量数据)"
    echo "2. 生产模式 (处理所有数据)"
    echo "3. 自定义参数运行"
    echo "4. 后台运行模式"
    echo "5. 查看帮助信息"
    echo "6. 退出"
    echo
}

# 测试模式
test_mode() {
    echo
    print_info "启动测试模式..."
    print_info "将处理少量数据用于测试"
    echo
    $PYTHON_CMD api_incremental_release_fetcher.py
}

# 生产模式
production_mode() {
    echo
    print_warning "启动生产模式..."
    print_warning "将处理大量数据，可能需要很长时间"
    echo
    read -p "确认启动生产模式？(y/N): " confirm
    if [[ $confirm =~ ^[Yy]$ ]]; then
        $PYTHON_CMD api_incremental_release_fetcher.py --production
    else
        print_info "已取消"
    fi
}

# 自定义参数模式
custom_mode() {
    echo
    print_info "自定义参数模式"
    echo
    echo "可用参数："
    echo "  --no-proxy        不使用代理"
    echo "  --production      生产模式"
    echo "  --start-id NUM    指定起始ID"
    echo "  --max-records NUM 最大处理记录数"
    echo
    read -p "请输入参数: " custom_args
    $PYTHON_CMD api_incremental_release_fetcher.py $custom_args
}

# 后台运行模式
background_mode() {
    echo
    print_info "后台运行模式"
    echo
    echo "1. 测试模式后台运行"
    echo "2. 生产模式后台运行"
    echo "3. 自定义参数后台运行"
    echo
    read -p "请选择 (1-3): " bg_choice
    
    case $bg_choice in
        1)
            print_info "启动测试模式后台运行..."
            nohup $PYTHON_CMD api_incremental_release_fetcher.py > api_fetcher.log 2>&1 &
            echo $! > api_fetcher.pid
            print_success "后台进程已启动，PID: $(cat api_fetcher.pid)"
            print_info "日志文件: api_fetcher.log"
            ;;
        2)
            print_warning "启动生产模式后台运行..."
            read -p "确认启动生产模式后台运行？(y/N): " confirm
            if [[ $confirm =~ ^[Yy]$ ]]; then
                nohup $PYTHON_CMD api_incremental_release_fetcher.py --production > api_fetcher.log 2>&1 &
                echo $! > api_fetcher.pid
                print_success "后台进程已启动，PID: $(cat api_fetcher.pid)"
                print_info "日志文件: api_fetcher.log"
            else
                print_info "已取消"
            fi
            ;;
        3)
            read -p "请输入参数: " custom_args
            nohup $PYTHON_CMD api_incremental_release_fetcher.py $custom_args > api_fetcher.log 2>&1 &
            echo $! > api_fetcher.pid
            print_success "后台进程已启动，PID: $(cat api_fetcher.pid)"
            print_info "日志文件: api_fetcher.log"
            ;;
        *)
            print_error "无效选择"
            ;;
    esac
}

# 显示帮助
help_mode() {
    echo
    print_info "帮助信息"
    $PYTHON_CMD api_incremental_release_fetcher.py --help
}

# 主循环
while true; do
    show_menu
    read -p "请输入选择 (1-6): " choice
    
    case $choice in
        1)
            test_mode
            break
            ;;
        2)
            production_mode
            break
            ;;
        3)
            custom_mode
            break
            ;;
        4)
            background_mode
            break
            ;;
        5)
            help_mode
            ;;
        6)
            print_info "再见！"
            exit 0
            ;;
        *)
            print_error "无效选择，请重新输入"
            ;;
    esac
    
    if [ "$choice" != "5" ]; then
        echo
        read -p "按回车键继续..."
        echo
    fi
done

echo
print_success "程序执行完成"
