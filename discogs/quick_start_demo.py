#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速启动演示脚本
展示如何在独立终端中运行API获取器
"""

import os
import sys
import subprocess
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def show_banner():
    """显示横幅"""
    print("=" * 60)
    print("   Discogs API Release 数据获取器 - 快速启动演示")
    print("=" * 60)
    print()

def show_available_scripts():
    """显示可用的脚本"""
    logger.info("📋 可用的启动方式:")
    print()
    
    scripts = [
        ("api_incremental_release_fetcher.py", "主程序", "python api_incremental_release_fetcher.py"),
        ("start_api_fetcher.bat", "Windows启动脚本", "start_api_fetcher.bat"),
        ("start_api_fetcher.sh", "Linux/macOS启动脚本", "./start_api_fetcher.sh"),
        ("check_environment.py", "环境检查", "python check_environment.py"),
    ]
    
    for i, (filename, description, command) in enumerate(scripts, 1):
        exists = "✅" if os.path.exists(filename) else "❌"
        print(f"{i}. {exists} {filename}")
        print(f"   描述: {description}")
        print(f"   命令: {command}")
        print()

def demonstrate_commands():
    """演示常用命令"""
    logger.info("💡 常用命令示例:")
    print()
    
    commands = [
        ("测试模式", "python api_incremental_release_fetcher.py"),
        ("生产模式", "python api_incremental_release_fetcher.py --production"),
        ("指定起始ID", "python api_incremental_release_fetcher.py --start-id 1000000"),
        ("限制记录数", "python api_incremental_release_fetcher.py --max-records 100"),
        ("不使用代理", "python api_incremental_release_fetcher.py --no-proxy"),
        ("查看帮助", "python api_incremental_release_fetcher.py --help"),
        ("环境检查", "python check_environment.py"),
    ]
    
    for description, command in commands:
        print(f"📌 {description}:")
        print(f"   {command}")
        print()

def show_file_structure():
    """显示文件结构"""
    logger.info("📁 当前目录文件结构:")
    print()
    
    files = [
        "api_incremental_release_fetcher.py",
        "start_api_fetcher.bat",
        "start_api_fetcher.sh", 
        "check_environment.py",
        "config_template.env",
        "README_独立运行.md",
        "quick_start_demo.py",
    ]
    
    for filename in files:
        exists = "✅" if os.path.exists(filename) else "❌"
        size = ""
        if os.path.exists(filename):
            size = f"({os.path.getsize(filename)} bytes)"
        print(f"{exists} {filename} {size}")

def run_environment_check():
    """运行环境检查"""
    logger.info("🔍 运行环境检查...")
    print()
    
    try:
        result = subprocess.run([
            sys.executable, "check_environment.py"
        ], capture_output=True, text=True, timeout=30)
        
        print("环境检查输出:")
        print("-" * 40)
        print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print("-" * 40)
            print(result.stderr)
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        logger.error("环境检查超时")
        return False
    except Exception as e:
        logger.error(f"环境检查失败: {e}")
        return False

def interactive_demo():
    """交互式演示"""
    logger.info("🎮 交互式演示模式")
    print()
    
    while True:
        print("请选择操作:")
        print("1. 查看帮助信息")
        print("2. 运行环境检查")
        print("3. 测试模式运行（仅显示命令）")
        print("4. 退出")
        print()
        
        choice = input("请输入选择 (1-4): ").strip()
        
        if choice == "1":
            print("\n" + "="*50)
            try:
                result = subprocess.run([
                    sys.executable, "api_incremental_release_fetcher.py", "--help"
                ], capture_output=True, text=True, timeout=10)
                print(result.stdout)
            except Exception as e:
                print(f"获取帮助信息失败: {e}")
            print("="*50 + "\n")
            
        elif choice == "2":
            print("\n" + "="*50)
            success = run_environment_check()
            if success:
                logger.info("✅ 环境检查通过")
            else:
                logger.error("❌ 环境检查失败")
            print("="*50 + "\n")
            
        elif choice == "3":
            print("\n" + "="*50)
            print("测试模式命令:")
            print("python api_incremental_release_fetcher.py")
            print()
            print("注意: 这里只显示命令，不实际运行")
            print("要实际运行，请在新终端中执行上述命令")
            print("="*50 + "\n")
            
        elif choice == "4":
            logger.info("退出演示")
            break
            
        else:
            print("无效选择，请重新输入\n")

def main():
    """主函数"""
    show_banner()
    
    logger.info("🚀 开始快速启动演示...")
    print()
    
    # 显示文件结构
    show_file_structure()
    print()
    
    # 显示可用脚本
    show_available_scripts()
    
    # 演示命令
    demonstrate_commands()
    
    # 提供交互选项
    print("=" * 60)
    choice = input("是否进入交互式演示模式？(y/N): ").strip().lower()
    
    if choice in ['y', 'yes']:
        interactive_demo()
    else:
        logger.info("演示结束")
        print()
        logger.info("💡 提示:")
        print("- 阅读 README_独立运行.md 获取详细说明")
        print("- 运行 python check_environment.py 检查环境")
        print("- 运行 python api_incremental_release_fetcher.py 开始测试")

if __name__ == "__main__":
    main()
