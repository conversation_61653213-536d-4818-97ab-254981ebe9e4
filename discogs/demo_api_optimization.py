#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
API优化功能演示脚本
演示新的浏览器模拟、智能重试和代理轮换功能的实际效果
"""

import sys
import time
import logging
from api_incremental_release_fetcher import EnhancedDiscogsAPIClient

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def demo_optimized_requests():
    """演示优化后的API请求"""
    logger.info("🚀 开始API优化功能演示...")
    
    print("=" * 60)
    print("Discogs API 优化功能演示")
    print("=" * 60)
    
    # 创建优化后的API客户端
    print("📱 初始化优化后的API客户端...")
    client = EnhancedDiscogsAPIClient(use_proxy=True)
    
    print("\n🔍 客户端配置信息:")
    print(f"- 账号数量: {len(client.account_manager.accounts)}")
    if client.proxy_manager:
        print(f"- 代理数量: {len(client.proxy_manager.proxy_configs)}")
    else:
        print("- 代理管理器: 未启用")
    print(f"- 最大延迟: {client.max_delay}秒")
    print(f"- 基础延迟: {client.base_delay}秒")
    
    # 演示测试ID列表
    test_ids = [1, 2, 3, 999999, 1000000]  # 包含存在和不存在的ID
    
    print(f"\n🧪 测试ID列表: {test_ids}")
    print("=" * 60)
    
    results = []
    
    for i, release_id in enumerate(test_ids, 1):
        print(f"\n📋 测试 {i}/{len(test_ids)}: Release ID {release_id}")
        print("-" * 40)
        
        start_time = time.time()
        
        try:
            # 调用优化后的API
            result = client.get_release(release_id)
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 分析结果
            if result is None:
                status = "404 Not Found"
                result_type = "None (404)"
            elif result == "429":
                status = "429 Rate Limited"
                result_type = "429 String"
            elif isinstance(result, dict):
                status = "200 Success"
                result_type = f"JSON ({len(result)} keys)"
            else:
                status = "Unknown"
                result_type = str(type(result))
            
            results.append({
                'id': release_id,
                'status': status,
                'result_type': result_type,
                'duration': duration
            })
            
            print(f"✅ 结果: {status}")
            print(f"📊 类型: {result_type}")
            print(f"⏱️  耗时: {duration:.2f}秒")
            
            # 显示部分数据（如果是成功的JSON）
            if isinstance(result, dict):
                print(f"📄 标题: {result.get('title', 'N/A')[:50]}...")
                print(f"🎵 艺术家: {result.get('artists', [{}])[0].get('name', 'N/A')[:30]}...")
            
        except KeyboardInterrupt:
            print("\n🛑 用户中断演示")
            break
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            results.append({
                'id': release_id,
                'status': f"Error: {type(e).__name__}",
                'result_type': str(e)[:50],
                'duration': duration
            })
            
            print(f"❌ 错误: {e}")
            print(f"⏱️  耗时: {duration:.2f}秒")
        
        # 显示账号和代理状态
        print(f"🔄 当前账号: {client.account_manager.current_account_index + 1}/{len(client.account_manager.accounts)}")
        if client.proxy_manager:
            current_proxy = client.proxy_manager.get_active_proxy()
            if current_proxy:
                print(f"🌐 当前代理: {current_proxy.name}")
        
        # 演示间隔
        if i < len(test_ids):
            print("⏳ 等待下次请求...")
            time.sleep(2)
    
    # 显示演示结果汇总
    print("\n" + "=" * 60)
    print("📊 演示结果汇总")
    print("=" * 60)
    
    if results:
        print(f"总请求数: {len(results)}")
        
        # 统计状态
        status_count = {}
        total_duration = 0
        
        for result in results:
            status = result['status']
            status_count[status] = status_count.get(status, 0) + 1
            total_duration += result['duration']
        
        print("\n状态分布:")
        for status, count in status_count.items():
            print(f"- {status}: {count} 次")
        
        print(f"\n平均耗时: {total_duration/len(results):.2f}秒")
        print(f"总耗时: {total_duration:.2f}秒")
        
        # 显示详细结果
        print("\n详细结果:")
        for result in results:
            print(f"ID {result['id']:>7}: {result['status']:<15} ({result['duration']:.2f}s)")
    
    # 显示优化效果说明
    print("\n" + "=" * 60)
    print("🎯 优化效果说明")
    print("=" * 60)
    print("✅ 浏览器身份模拟: 每个账号使用不同的真实浏览器标识")
    print("✅ 智能重试策略: 429错误最大等待5秒，使用指数退避+随机抖动")
    print("✅ 动态代理轮换: 自动切换代理IP，提高请求成功率")
    print("✅ 人性化行为: 随机延迟模拟真实用户访问模式")
    print("✅ 详细日志记录: 完整的请求流程和状态监控")
    
    # 获取客户端统计信息
    stats = client.get_stats()
    if stats:
        print(f"\n📈 客户端统计:")
        for key, value in stats.items():
            print(f"- {key}: {value}")
    
    print("\n💡 使用建议:")
    print("- 在生产环境中使用 --production 参数")
    print("- 监控日志中的429错误频率")
    print("- 根据网络状况调整代理配置")
    print("- 定期检查账号状态和代理可用性")
    
    return True

def main():
    """主函数"""
    print("Discogs API 优化功能演示")
    print("此演示将展示新的浏览器模拟、智能重试和代理轮换功能")
    print()
    
    # 询问用户是否继续
    try:
        response = input("是否开始演示？(y/N): ").strip().lower()
        if response not in ['y', 'yes', '是']:
            print("演示已取消")
            return 0
    except KeyboardInterrupt:
        print("\n演示已取消")
        return 0
    
    try:
        success = demo_optimized_requests()
        
        if success:
            print("\n🎉 演示完成！")
            print("优化功能正常工作，可以开始使用优化后的API获取器")
        else:
            print("\n❌ 演示过程中出现问题")
            print("请检查网络连接和配置")
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n🛑 演示被用户中断")
        return 0
    except Exception as e:
        logger.error(f"演示过程中出现异常: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
