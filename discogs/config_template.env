# Discogs API 数据获取器配置文件模板
# 复制此文件为 .env 并根据需要修改配置

# ================================
# 数据库配置
# ================================

# MongoDB连接URI
# 格式: mongodb://用户名:密码@主机:端口/数据库名
MONGO_URI=**********************************************************

# 数据库名称
DB_NAME=music_test

# ================================
# API配置
# ================================

# API基础URL (通常不需要修改)
API_BASE_URL=https://api.discogs.com/releases

# API请求间隔 (秒)
API_RATE_LIMIT=1.0

# API请求超时时间 (秒)
API_TIMEOUT=30

# 最大重试次数
MAX_RETRIES=3

# ================================
# 代理配置 (如需要)
# ================================

# 主代理配置
# HTTP_PROXY=http://127.0.0.1:7897
# HTTPS_PROXY=http://127.0.0.1:7897

# 备用代理配置（支持多代理轮换）
# PROXY_2_HTTP=http://127.0.0.1:7890
# PROXY_2_HTTPS=http://127.0.0.1:7890
# PROXY_3_HTTP=http://127.0.0.1:1087
# PROXY_3_HTTPS=http://127.0.0.1:1087

# ================================
# 智能重试配置
# ================================

# 基础延迟时间（秒）
BASE_DELAY=1.0

# 最大延迟时间（秒）- 针对429错误
MAX_DELAY_429=5.0

# 最大重试次数
MAX_RETRIES=3

# 是否启用随机抖动
ENABLE_JITTER=true

# 随机请求延迟范围（秒）
MIN_REQUEST_DELAY=0.8
MAX_REQUEST_DELAY=2.0

# ================================
# 运行配置
# ================================

# 测试模式记录数量
TEST_RECORDS_COUNT=10

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# 是否启用详细日志
VERBOSE_LOGGING=true

# ================================
# 数据表名配置
# ================================

# 成功数据表名
TARGET_COLLECTION_NAME=release_new

# 429限流数据表名
COPY_TARGET_COLLECTION_NAME=release_copy

# 404错误数据表名
NOT_FOUND_COLLECTION_NAME=release_404

# ================================
# 进度保存配置
# ================================

# 进度文件路径
PROGRESS_FILE=progress.json

# 进度保存间隔 (处理多少条记录后保存一次)
PROGRESS_SAVE_INTERVAL=10

# ================================
# 浏览器模拟配置
# ================================

# 是否启用浏览器身份模拟
ENABLE_BROWSER_SIMULATION=true

# 浏览器User-Agent使用概率（0.0-1.0）
BROWSER_UA_PROBABILITY=0.9

# 是否启用完整请求头模拟
ENABLE_FULL_HEADERS=true

# ================================
# 使用说明
# ================================

# 1. 复制此文件为 .env
# 2. 根据实际情况修改配置值
# 3. 取消注释需要的配置项 (删除行首的 #)
# 4. 保存文件后重新运行程序

# 示例：启用多代理轮换
# HTTP_PROXY=http://127.0.0.1:7897
# HTTPS_PROXY=http://127.0.0.1:7897
# PROXY_2_HTTP=http://127.0.0.1:7890
# PROXY_2_HTTPS=http://127.0.0.1:7890

# 示例：调整重试策略
# BASE_DELAY=1.5
# MAX_DELAY_429=3.0
# MAX_RETRIES=5

# 示例：修改日志级别为DEBUG
# LOG_LEVEL=DEBUG

# 示例：增加测试记录数量
# TEST_RECORDS_COUNT=50

# ================================
# 优化建议
# ================================

# 1. 网络优化：
#    - 配置多个代理服务器实现IP轮换
#    - 使用稳定的代理服务避免频繁切换
#    - 监控代理服务器的响应时间和成功率

# 2. 请求策略优化：
#    - 适当调整BASE_DELAY和MAX_DELAY_429
#    - 启用ENABLE_JITTER减少请求模式识别
#    - 根据API响应调整MIN/MAX_REQUEST_DELAY

# 3. 浏览器模拟优化：
#    - 保持ENABLE_BROWSER_SIMULATION=true
#    - 适当调整BROWSER_UA_PROBABILITY
#    - 启用ENABLE_FULL_HEADERS模拟真实浏览器
